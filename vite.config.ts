import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      // Match the baseUrl from tsconfig.json
      '@': resolve(__dirname, 'src'),
      components: resolve(__dirname, 'src/components'),
      context: resolve(__dirname, 'src/context'),
      hooks: resolve(__dirname, 'src/hooks'),
      layout: resolve(__dirname, 'src/layout'),
      lib: resolve(__dirname, 'src/lib'),
      modules: resolve(__dirname, 'src/modules'),
      network: resolve(__dirname, 'src/network'),
      pages: resolve(__dirname, 'src/pages'),
      store: resolve(__dirname, 'src/store'),
      theme: resolve(__dirname, 'src/theme.ts')
    }
  },
  server: {
    port: 3000,
    open: false // Matches BROWSER=none from .env
  },
  build: {
    outDir: 'build',
    sourcemap: true
  },
  // Handle environment variables
  envPrefix: 'VITE_',
  // Test configuration
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/setupTests.ts']
  }
})
