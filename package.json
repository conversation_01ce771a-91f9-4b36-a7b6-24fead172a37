{"name": "voxto-pwa", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@hookform/error-message": "^2.0.1", "@mdx-js/react": "^3.1.0", "@nivo/core": "^0.99.0", "@nivo/line": "^0.99.0", "@nivo/pie": "^0.99.0", "@nivo/tooltip": "^0.99.0", "@theme-ui/match-media": "^0.17.2", "@theme-ui/tailwind": "^0.17.2", "axios": "^1.9.0", "comma-number": "^2.1.0", "country-state-city": "^3.2.1", "framer-motion": "6.5.1", "human-date": "^1.4.0", "lodash": "^4.17.21", "luxon": "^3.6.1", "markdown-to-jsx": "^7.7.6", "qrcode": "^1.5.4", "qs": "^6.14.0", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-hook-form": "7.45.0", "react-icons": "4.12.0", "react-input-verification-code": "^1.0.2", "react-modal": "3.15.1", "react-phone-input-2": "^2.15.1", "react-router-dom": "^6.30.1", "react-scroll-percentage": "^4.3.2", "react-stacked-center-carousel": "^1.0.14", "react-use-websocket": "^4.13.0", "styled-components": "^6.1.18", "swr": "^2.3.3", "theme-ui": "^0.17.2", "usehooks-ts": "^2.16.0", "valtio": "1.11.2"}, "devDependencies": {"@fontsource/poppins": "^5.2.6", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/comma-number": "^2.1.2", "@types/human-date": "^1.4.5", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/luxon": "^3.6.2", "@types/node": "^22.15.29", "@types/qrcode": "^1.5.5", "@types/qs": "^6.14.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-modal": "3.13.1", "@vitejs/plugin-react": "^4.5.0", "@vitest/ui": "^3.1.4", "jsdom": "^26.1.0", "typescript": "^4.9.5", "vite": "^6.3.5", "vitest": "^3.1.4", "web-vitals": "^5.0.2", "workbox-background-sync": "^7.3.0", "workbox-broadcast-update": "^7.3.0", "workbox-cacheable-response": "^7.3.0", "workbox-core": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-google-analytics": "^7.3.0", "workbox-navigation-preload": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-range-requests": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-streams": "^7.3.0"}, "scripts": {"dev": "vite", "start": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}