<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="368" height="336" viewBox="0 0 368 336">
  <defs>
    <clipPath id="clip-path">
      <path id="Masking" d="M50,0H368a0,0,0,0,1,0,0V336a0,0,0,0,1,0,0H0a0,0,0,0,1,0,0V50A50,50,0,0,1,50,0Z" transform="translate(1072)" fill="#161a42"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.275" x2="0.003" y2="0.704" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1142a4"/>
      <stop offset="1" stop-color="#0ba2e4"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.275" x2="0" y2="0.811" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#6e0b4a"/>
      <stop offset="1" stop-color="#f70f81"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.275" y1="0" x2="0" y2="0.811" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-4" x1="0.5" x2="0.5" y2="1" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-6" x1="0.275" x2="0.003" y2="0.704" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#6e0b4a"/>
      <stop offset="1" stop-color="#c20a51"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="0.5" y1="0" x2="0.5" y2="1" xlink:href="#linear-gradient"/>
  </defs>
  <g id="Ornament" transform="translate(-1072)">
    <g id="Masking-2" data-name="Masking" clip-path="url(#clip-path)">
      <circle id="Shadow_2" data-name="Shadow  2" cx="51" cy="51" r="51" transform="translate(1135.193 163.871) rotate(-168.682)" fill="#a22dff" opacity="0.3"/>
      <circle id="Ornament_7" data-name="Ornament 7" cx="24.286" cy="24.286" r="24.286" transform="translate(1420.952 -13.324) rotate(11.318)" fill="url(#linear-gradient)"/>
      <circle id="Ornament_6" data-name="Ornament 6" cx="33.5" cy="33.5" r="33.5" transform="translate(1402.019 189.447) rotate(11.318)" fill="url(#linear-gradient-2)"/>
      <circle id="Ornament_5" data-name="Ornament 5" cx="11" cy="11" r="11" transform="translate(1146.587 38.8) rotate(11.318)" fill="url(#linear-gradient-3)"/>
      <circle id="Ornament_4" data-name="Ornament 4" cx="4" cy="4" r="4" transform="translate(1390.44 105.31) rotate(11.318)" fill="url(#linear-gradient-4)"/>
      <circle id="Ornament_3" data-name="Ornament 3" cx="4" cy="4" r="4" transform="translate(1116.98 149.44) rotate(11.318)" fill="url(#linear-gradient-4)"/>
      <circle id="Ornament_2" data-name="Ornament 2" cx="33.5" cy="33.5" r="33.5" transform="translate(1094.858 149.441) rotate(-168.682)" fill="url(#linear-gradient-6)"/>
      <circle id="Ornament_1" data-name="Ornament 1" cx="9" cy="9" r="9" transform="translate(1364.65 170.623) rotate(-168.682)" fill="url(#linear-gradient)"/>
      <circle id="Shadow_1" data-name="Shadow  1" cx="37.5" cy="37.5" r="37.5" transform="translate(1457.705 291.931) rotate(-168.682)" fill="#ffa336" opacity="0.5"/>
      <circle id="Shadow_3" data-name="Shadow  3" cx="25.242" cy="25.242" r="25.242" transform="translate(1105.503 259.45) rotate(-168.682)" opacity="0.5" fill="url(#linear-gradient-8)"/>
    </g>
  </g>
</svg>
