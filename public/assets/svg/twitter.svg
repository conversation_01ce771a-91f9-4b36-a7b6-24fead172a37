<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="17.611" height="14.331" viewBox="0 0 17.611 14.331">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#1142a4"/>
      <stop offset="1" stop-color="#0ba2e4"/>
    </linearGradient>
  </defs>
  <path id="_x30_4.Twitter" d="M27.511,19.2a7.406,7.406,0,0,1-2.069.572,3.522,3.522,0,0,0,1.585-2,7.6,7.6,0,0,1-2.289.881,3.613,3.613,0,0,0-6.252,2.466,3.3,3.3,0,0,0,.088.815,10.162,10.162,0,0,1-7.419-3.764,3.623,3.623,0,0,0,1.1,4.821,3.466,3.466,0,0,1-1.629-.44,3.648,3.648,0,0,0,2.884,3.588,3.962,3.962,0,0,1-1.629.066,3.625,3.625,0,0,0,3.368,2.51A7.274,7.274,0,0,1,9.9,30.2a10.259,10.259,0,0,0,15.806-8.629,3.251,3.251,0,0,0-.022-.462A7.166,7.166,0,0,0,27.511,19.2Z" transform="translate(-9.9 -17.5)" fill="url(#linear-gradient)"/>
</svg>
