import client, { IDataResponse, serialize } from '../request'

export type NetworkState = {
  id: number
  name: string
  blocked: boolean
  created_at: string
  updated_at: string
}

// useSWR, return strings
const getAll = '/networks'

// useSWR, return strings
const get = (id: string | number) => {
  return `/networks/${id}`
}

// useSWR, return strings
const getByToken = (token: string) => {
  return serialize(`/networks/by-token`, { symbol: token })
}

// axios
const create = (data: NetworkState) => {
  return client.post('/networks', data)
}

// axios
const update = (id: string | number, data: NetworkState) => {
  return client.put(`/networks/${id}`, data)
}

// processing
const toRow = (data: IDataResponse<NetworkState>): NetworkState[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id
      }
    })
  }

  return []
}

// processing
const toPaginate = (data: IDataResponse<NetworkState>) => {
  return {
    total: data?.meta?.total ?? 0
  }
}

// axios
const remove = (id: string | number) => {
  return client.delete(`/networks/${id}`)
}

const NetworkService = {
  getAll,
  get,
  getByToken,
  create,
  update,
  toPaginate,
  toRow,
  remove
}

export default NetworkService
