import { FC } from 'react'
import { Box, Flex, Image, Input, Text } from 'theme-ui'

const TwoFactorAuthenticationStep3: FC<{ backupKey: string }> = ({ backupKey }) => {
  return (
    <Box>
      <Box>
        <Text variant="large">Step 3</Text>
      </Box>
      <Box p={1} />
      <Box>
        <Text color="secondaryText" variant="mediumSmall">
          Please save this key on paper. This key will allow you to recover your Google
          Authenticator in case of phone loss.
        </Text>
      </Box>
      <Box p={5} />
      <Box>
        <Flex
          variant="layout.flexCenterCenter"
          sx={{
            flexDirection: ['column', 'row'],
            alignItems: ['flex-start', 'center'],
            justifyContent: 'flex-start'
          }}
        >
          <Image src="/assets/svg/pencil.svg" sx={{ width: [120, 230], height: ['auto', 230] }} />
          <Box sx={{ ml: [0, 15], width: ['100%', 500], pt: 5 }}>
            <Box sx={{ width: ['100%', 300, 400] }}>
              <Text>
                Resetting your Google Authentication requires opening a support ticket and takes at
                least 7 days to process.
              </Text>
            </Box>
            <Box p={5} />
            <Flex variant="layout.flexCenterStart" sx={{ width: ['100%', 300, 350] }}>
              <Input sx={{ borderRadius: 30, mr: 5 }} disabled value={backupKey} />
            </Flex>
          </Box>
        </Flex>
      </Box>
    </Box>
  )
}

export default TwoFactorAuthenticationStep3
