import Tab from 'components/Tab'
import { Box } from 'theme-ui'
import ShopActive from './shop-active'
import { useState } from 'react'
import CompletedListing from './completed-listing'
import { AnimatePresence, motion } from 'framer-motion'
import { fadeAnim } from 'lib/animation'

let AnimatedBox = motion(Box)

const NFTShop = () => {
  const [currentTab, setCurrentTab] = useState<number>(0)

  return (
    <Box>
      <Tab
        defaultTab={currentTab}
        tabs={['Active Listing', 'Completed Listing']}
        onTabChange={setCurrentTab}
      />
      <Box p={3} />

      <AnimatePresence exitBeforeEnter>
        {
          {
            0: (
              <AnimatedBox
                key="active"
                initial="hide"
                animate="show"
                exit="hide"
                variants={fadeAnim}
              >
                <ShopActive />
              </AnimatedBox>
            ),
            1: (
              <AnimatedBox
                key="completed"
                initial="hide"
                animate="show"
                exit="hide"
                variants={fadeAnim}
              >
                <CompletedListing />
              </AnimatedBox>
            )
          }[currentTab]
        }
      </AnimatePresence>
    </Box>
  )
}

export default NFTShop
