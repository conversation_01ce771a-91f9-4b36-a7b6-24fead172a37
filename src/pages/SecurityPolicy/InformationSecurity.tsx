import React, { useState } from 'react'
import { Box, Card, Heading, Text, Link } from 'theme-ui'
import Markdown from 'markdown-to-jsx'

const markdownFile = `
# Information security policies

## Management direction for information security

### Exception definition

Situations in which the rationales and/or requirements in the Sample Description Security Policy cannot be adhered to must be registered as an exception.

Example: Because an application will stop running when a security patch is applied to an Operating System, the security patch cannot be applied.

### Registering exceptions

Registered exceptions must contain at least the following information:

1. Reference to requirement,
2. Object, 
3. Unmitigated vulnerability, 
4. Compensating measure(s) 
5. Duration.

### Central register

Exceptions must registered in a central Exception Register.

### Handling exceptions

For each exception a risk assessment must be performed. For identified risks possible compensating controls need to be analysed and identified. Try isolating a system with known vulnerabilities that cannot be patched, to cover the risk of being hacked.

### Compensating controls

Compensating controls must be implemented to mitigate the risks that exist as a result of not complying to the rationales and requirements in the Sample Description Security Policy.

### Risk acceptance

In case no (full) compensating controls are identified or compensating controls would have considerable financial consequences, the exception must be assessed by Sample Description CISO (or CSO for appointed topics) and adequate follow up must be determined (such as risk acceptance).

NOTE: Risks can only be accepted by the CISO (or CSO for appointed topics).

### Review

Registered exceptions must be reviewed on the due date and at least yearly to assess (1) whether compensating controls still mitigate the risk or (2) the motivations for accepting the risk are still valid.
`

const InformationSecurityPage = () => {
  const [content] = useState({ md: markdownFile })

  return (
    <Box
      variant="layout.newTabContainer"
      sx={{
        minHeight: ['calc(30vh - 51px - 144px)', 'inherit'],
        py: 5,
        position: 'relative'
      }}
    >
      <Card p={[1, 10]}>
        <Markdown
          children={content.md}
          options={{
            overrides: {
              h1: {
                component: Heading,
                props: {
                  sx: { textAlign: 'center' }
                }
              },
              h2: {
                component: Heading,
                props: {
                  as: 'h2',
                  sx: {
                    my: '12px'
                  }
                }
              },
              h3: {
                component: Heading,
                props: {
                  as: 'h3'
                }
              },
              p: {
                component: Text,
                props: {
                  as: 'p',
                  sx: {
                    lineHeight: 'initial',
                    marginBottom: '12px'
                  }
                }
              },
              a: {
                component: Link,
                props: {
                  sx: {
                    color: 'white',
                    textDecoration: 'underline'
                  }
                }
              }
            }
          }}
        />
      </Card>
    </Box>
  )
}

export default InformationSecurityPage
