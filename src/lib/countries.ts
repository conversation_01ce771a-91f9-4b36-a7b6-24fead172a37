export interface Country {
  name: string
  isoCode: string
  phonecode: string
}

export const countries: Country[] = [
  {
    name: 'Afghanistan',
    phonecode: '+93',
    isoCode: 'AF'
  },
  {
    name: 'Aland Islands',
    phonecode: '+358',
    isoCode: 'AX'
  },
  {
    name: 'Albania',
    phonecode: '+355',
    isoCode: 'AL'
  },
  {
    name: 'Algeria',
    phonecode: '+213',
    isoCode: 'DZ'
  },
  {
    name: 'AmericanSamoa',
    phonecode: '+1684',
    isoCode: 'AS'
  },
  {
    name: 'Andorra',
    phonecode: '+376',
    isoCode: 'AD'
  },
  {
    name: 'Angola',
    phonecode: '+244',
    isoCode: 'AO'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    phonecode: '+1264',
    isoCode: 'AI'
  },
  {
    name: 'Antarctica',
    phonecode: '+672',
    isoCode: 'AQ'
  },
  {
    name: 'Antigua and Barbuda',
    phonecode: '+1268',
    isoCode: 'AG'
  },
  {
    name: 'Argentina',
    phonecode: '+54',
    isoCode: 'AR'
  },
  {
    name: 'Armenia',
    phonecode: '+374',
    isoCode: 'AM'
  },
  {
    name: 'Aruba',
    phonecode: '+297',
    isoCode: 'AW'
  },
  {
    name: 'Australia',
    phonecode: '+61',
    isoCode: 'AU'
  },
  {
    name: 'Austria',
    phonecode: '+43',
    isoCode: 'AT'
  },
  {
    name: 'Azerbaijan',
    phonecode: '+994',
    isoCode: 'AZ'
  },
  {
    name: 'Bahamas',
    phonecode: '+1242',
    isoCode: 'BS'
  },
  {
    name: 'Bahrain',
    phonecode: '+973',
    isoCode: 'BH'
  },
  {
    name: 'Bangladesh',
    phonecode: '+880',
    isoCode: 'BD'
  },
  {
    name: 'Barbados',
    phonecode: '+1246',
    isoCode: 'BB'
  },
  {
    name: 'Belarus',
    phonecode: '+375',
    isoCode: 'BY'
  },
  {
    name: 'Belgium',
    phonecode: '+32',
    isoCode: 'BE'
  },
  {
    name: 'Belize',
    phonecode: '+501',
    isoCode: 'BZ'
  },
  {
    name: 'Benin',
    phonecode: '+229',
    isoCode: 'BJ'
  },
  {
    name: 'Bermuda',
    phonecode: '+1441',
    isoCode: 'BM'
  },
  {
    name: 'Bhutan',
    phonecode: '+975',
    isoCode: 'BT'
  },
  {
    name: 'Bolivia, Plurinational State of',
    phonecode: '+591',
    isoCode: 'BO'
  },
  {
    name: 'Bosnia and Herzegovina',
    phonecode: '+387',
    isoCode: 'BA'
  },
  {
    name: 'Botswana',
    phonecode: '+267',
    isoCode: 'BW'
  },
  {
    name: 'Brazil',
    phonecode: '+55',
    isoCode: 'BR'
  },
  {
    name: 'British Indian Ocean Territory',
    phonecode: '+246',
    isoCode: 'IO'
  },
  {
    name: 'Brunei Darussalam',
    phonecode: '+673',
    isoCode: 'BN'
  },
  {
    name: 'Bulgaria',
    phonecode: '+359',
    isoCode: 'BG'
  },
  {
    name: 'Burkina Faso',
    phonecode: '+226',
    isoCode: 'BF'
  },
  {
    name: 'Burundi',
    phonecode: '+257',
    isoCode: 'BI'
  },
  {
    name: 'Cambodia',
    phonecode: '+855',
    isoCode: 'KH'
  },
  {
    name: 'Cameroon',
    phonecode: '+237',
    isoCode: 'CM'
  },
  {
    name: 'Canada',
    phonecode: '+1',
    isoCode: 'CA'
  },
  {
    name: 'Cape Verde',
    phonecode: '+238',
    isoCode: 'CV'
  },
  {
    name: 'Cayman Islands',
    phonecode: '+ 345',
    isoCode: 'KY'
  },
  {
    name: 'Central African Republic',
    phonecode: '+236',
    isoCode: 'CF'
  },
  {
    name: 'Chad',
    phonecode: '+235',
    isoCode: 'TD'
  },
  {
    name: 'Chile',
    phonecode: '+56',
    isoCode: 'CL'
  },
  {
    name: 'China',
    phonecode: '+86',
    isoCode: 'CN'
  },
  {
    name: 'Christmas Island',
    phonecode: '+61',
    isoCode: 'CX'
  },
  {
    name: 'Cocos (Keeling) Islands',
    phonecode: '+61',
    isoCode: 'CC'
  },
  {
    name: 'Colombia',
    phonecode: '+57',
    isoCode: 'CO'
  },
  {
    name: 'Comoros',
    phonecode: '+269',
    isoCode: 'KM'
  },
  {
    name: 'Congo',
    phonecode: '+242',
    isoCode: 'CG'
  },
  {
    name: 'Congo, The Democratic Republic of the Congo',
    phonecode: '+243',
    isoCode: 'CD'
  },
  {
    name: 'Cook Islands',
    phonecode: '+682',
    isoCode: 'CK'
  },
  {
    name: 'Costa Rica',
    phonecode: '+506',
    isoCode: 'CR'
  },
  {
    name: "Cote d'Ivoire",
    phonecode: '+225',
    isoCode: 'CI'
  },
  {
    name: 'Croatia',
    phonecode: '+385',
    isoCode: 'HR'
  },
  {
    name: 'Cuba',
    phonecode: '+53',
    isoCode: 'CU'
  },
  {
    name: 'Cyprus',
    phonecode: '+357',
    isoCode: 'CY'
  },
  {
    name: 'Czech Republic',
    phonecode: '+420',
    isoCode: 'CZ'
  },
  {
    name: 'Denmark',
    phonecode: '+45',
    isoCode: 'DK'
  },
  {
    name: 'Djibouti',
    phonecode: '+253',
    isoCode: 'DJ'
  },
  {
    name: 'Dominica',
    phonecode: '+1767',
    isoCode: 'DM'
  },
  {
    name: 'Dominican Republic',
    phonecode: '+1849',
    isoCode: 'DO'
  },
  {
    name: 'Ecuador',
    phonecode: '+593',
    isoCode: 'EC'
  },
  {
    name: 'Egypt',
    phonecode: '+20',
    isoCode: 'EG'
  },
  {
    name: 'El Salvador',
    phonecode: '+503',
    isoCode: 'SV'
  },
  {
    name: 'Equatorial Guinea',
    phonecode: '+240',
    isoCode: 'GQ'
  },
  {
    name: 'Eritrea',
    phonecode: '+291',
    isoCode: 'ER'
  },
  {
    name: 'Estonia',
    phonecode: '+372',
    isoCode: 'EE'
  },
  {
    name: 'Ethiopia',
    phonecode: '+251',
    isoCode: 'ET'
  },
  {
    name: 'Falkland Islands (Malvinas)',
    phonecode: '+500',
    isoCode: 'FK'
  },
  {
    name: 'Faroe Islands',
    phonecode: '+298',
    isoCode: 'FO'
  },
  {
    name: 'Fiji',
    phonecode: '+679',
    isoCode: 'FJ'
  },
  {
    name: 'Finland',
    phonecode: '+358',
    isoCode: 'FI'
  },
  {
    name: 'France',
    phonecode: '+33',
    isoCode: 'FR'
  },
  {
    name: 'French Guiana',
    phonecode: '+594',
    isoCode: 'GF'
  },
  {
    name: 'French Polynesia',
    phonecode: '+689',
    isoCode: 'PF'
  },
  {
    name: 'Gabon',
    phonecode: '+241',
    isoCode: 'GA'
  },
  {
    name: 'Gambia',
    phonecode: '+220',
    isoCode: 'GM'
  },
  {
    name: 'Georgia',
    phonecode: '+995',
    isoCode: 'GE'
  },
  {
    name: 'Germany',
    phonecode: '+49',
    isoCode: 'DE'
  },
  {
    name: 'Ghana',
    phonecode: '+233',
    isoCode: 'GH'
  },
  {
    name: 'Gibraltar',
    phonecode: '+350',
    isoCode: 'GI'
  },
  {
    name: 'Greece',
    phonecode: '+30',
    isoCode: 'GR'
  },
  {
    name: 'Greenland',
    phonecode: '+299',
    isoCode: 'GL'
  },
  {
    name: 'Grenada',
    phonecode: '+1473',
    isoCode: 'GD'
  },
  {
    name: 'Guadeloupe',
    phonecode: '+590',
    isoCode: 'GP'
  },
  {
    name: 'Guam',
    phonecode: '+1671',
    isoCode: 'GU'
  },
  {
    name: 'Guatemala',
    phonecode: '+502',
    isoCode: 'GT'
  },
  {
    name: 'Guernsey',
    phonecode: '+44',
    isoCode: 'GG'
  },
  {
    name: 'Guinea',
    phonecode: '+224',
    isoCode: 'GN'
  },
  {
    name: 'Guinea-Bissau',
    phonecode: '+245',
    isoCode: 'GW'
  },
  {
    name: 'Guyana',
    phonecode: '+595',
    isoCode: 'GY'
  },
  {
    name: 'Haiti',
    phonecode: '+509',
    isoCode: 'HT'
  },
  {
    name: 'Holy See (Vatican City State)',
    phonecode: '+379',
    isoCode: 'VA'
  },
  {
    name: 'Honduras',
    phonecode: '+504',
    isoCode: 'HN'
  },
  {
    name: 'Hong Kong',
    phonecode: '+852',
    isoCode: 'HK'
  },
  {
    name: 'Hungary',
    phonecode: '+36',
    isoCode: 'HU'
  },
  {
    name: 'Iceland',
    phonecode: '+354',
    isoCode: 'IS'
  },
  {
    name: 'India',
    phonecode: '+91',
    isoCode: 'IN'
  },
  {
    name: 'Indonesia',
    phonecode: '+62',
    isoCode: 'ID'
  },
  {
    name: 'Iran, Islamic Republic of Persian Gulf',
    phonecode: '+98',
    isoCode: 'IR'
  },
  {
    name: 'Iraq',
    phonecode: '+964',
    isoCode: 'IQ'
  },
  {
    name: 'Ireland',
    phonecode: '+353',
    isoCode: 'IE'
  },
  {
    name: 'Isle of Man',
    phonecode: '+44',
    isoCode: 'IM'
  },
  {
    name: 'Israel',
    phonecode: '+972',
    isoCode: 'IL'
  },
  {
    name: 'Italy',
    phonecode: '+39',
    isoCode: 'IT'
  },
  {
    name: 'Jamaica',
    phonecode: '+1876',
    isoCode: 'JM'
  },
  {
    name: 'Japan',
    phonecode: '+81',
    isoCode: 'JP'
  },
  {
    name: 'Jersey',
    phonecode: '+44',
    isoCode: 'JE'
  },
  {
    name: 'Jordan',
    phonecode: '+962',
    isoCode: 'JO'
  },
  {
    name: 'Kazakhstan',
    phonecode: '+77',
    isoCode: 'KZ'
  },
  {
    name: 'Kenya',
    phonecode: '+254',
    isoCode: 'KE'
  },
  {
    name: 'Kiribati',
    phonecode: '+686',
    isoCode: 'KI'
  },
  {
    name: "Korea, Democratic People's Republic of Korea",
    phonecode: '+850',
    isoCode: 'KP'
  },
  {
    name: 'Korea, Republic of South Korea',
    phonecode: '+82',
    isoCode: 'KR'
  },
  {
    name: 'Kuwait',
    phonecode: '+965',
    isoCode: 'KW'
  },
  {
    name: 'Kyrgyzstan',
    phonecode: '+996',
    isoCode: 'KG'
  },
  {
    name: 'Laos',
    phonecode: '+856',
    isoCode: 'LA'
  },
  {
    name: 'Latvia',
    phonecode: '+371',
    isoCode: 'LV'
  },
  {
    name: 'Lebanon',
    phonecode: '+961',
    isoCode: 'LB'
  },
  {
    name: 'Lesotho',
    phonecode: '+266',
    isoCode: 'LS'
  },
  {
    name: 'Liberia',
    phonecode: '+231',
    isoCode: 'LR'
  },
  {
    name: 'Libyan Arab Jamahiriya',
    phonecode: '+218',
    isoCode: 'LY'
  },
  {
    name: 'Liechtenstein',
    phonecode: '+423',
    isoCode: 'LI'
  },
  {
    name: 'Lithuania',
    phonecode: '+370',
    isoCode: 'LT'
  },
  {
    name: 'Luxembourg',
    phonecode: '+352',
    isoCode: 'LU'
  },
  {
    name: 'Macao',
    phonecode: '+853',
    isoCode: 'MO'
  },
  {
    name: 'Macedonia',
    phonecode: '+389',
    isoCode: 'MK'
  },
  {
    name: 'Madagascar',
    phonecode: '+261',
    isoCode: 'MG'
  },
  {
    name: 'Malawi',
    phonecode: '+265',
    isoCode: 'MW'
  },
  {
    name: 'Malaysia',
    phonecode: '+60',
    isoCode: 'MY'
  },
  {
    name: 'Maldives',
    phonecode: '+960',
    isoCode: 'MV'
  },
  {
    name: 'Mali',
    phonecode: '+223',
    isoCode: 'ML'
  },
  {
    name: 'Malta',
    phonecode: '+356',
    isoCode: 'MT'
  },
  {
    name: 'Marshall Islands',
    phonecode: '+692',
    isoCode: 'MH'
  },
  {
    name: 'Martinique',
    phonecode: '+596',
    isoCode: 'MQ'
  },
  {
    name: 'Mauritania',
    phonecode: '+222',
    isoCode: 'MR'
  },
  {
    name: 'Mauritius',
    phonecode: '+230',
    isoCode: 'MU'
  },
  {
    name: 'Mayotte',
    phonecode: '+262',
    isoCode: 'YT'
  },
  {
    name: 'Mexico',
    phonecode: '+52',
    isoCode: 'MX'
  },
  {
    name: 'Micronesia, Federated States of Micronesia',
    phonecode: '+691',
    isoCode: 'FM'
  },
  {
    name: 'Moldova',
    phonecode: '+373',
    isoCode: 'MD'
  },
  {
    name: 'Monaco',
    phonecode: '+377',
    isoCode: 'MC'
  },
  {
    name: 'Mongolia',
    phonecode: '+976',
    isoCode: 'MN'
  },
  {
    name: 'Montenegro',
    phonecode: '+382',
    isoCode: 'ME'
  },
  {
    name: 'Montserrat',
    phonecode: '+1664',
    isoCode: 'MS'
  },
  {
    name: 'Morocco',
    phonecode: '+212',
    isoCode: 'MA'
  },
  {
    name: 'Mozambique',
    phonecode: '+258',
    isoCode: 'MZ'
  },
  {
    name: 'Myanmar',
    phonecode: '+95',
    isoCode: 'MM'
  },
  {
    name: 'Namibia',
    phonecode: '+264',
    isoCode: 'NA'
  },
  {
    name: 'Nauru',
    phonecode: '+674',
    isoCode: 'NR'
  },
  {
    name: 'Nepal',
    phonecode: '+977',
    isoCode: 'NP'
  },
  {
    name: 'Netherlands',
    phonecode: '+31',
    isoCode: 'NL'
  },
  {
    name: 'Netherlands Antilles',
    phonecode: '+599',
    isoCode: 'AN'
  },
  {
    name: 'New Caledonia',
    phonecode: '+687',
    isoCode: 'NC'
  },
  {
    name: 'New Zealand',
    phonecode: '+64',
    isoCode: 'NZ'
  },
  {
    name: 'Nicaragua',
    phonecode: '+505',
    isoCode: 'NI'
  },
  {
    name: 'Niger',
    phonecode: '+227',
    isoCode: 'NE'
  },
  {
    name: 'Nigeria',
    phonecode: '+234',
    isoCode: 'NG'
  },
  {
    name: 'Niue',
    phonecode: '+683',
    isoCode: 'NU'
  },
  {
    name: 'Norfolk Island',
    phonecode: '+672',
    isoCode: 'NF'
  },
  {
    name: 'Northern Mariana Islands',
    phonecode: '+1670',
    isoCode: 'MP'
  },
  {
    name: 'Norway',
    phonecode: '+47',
    isoCode: 'NO'
  },
  {
    name: 'Oman',
    phonecode: '+968',
    isoCode: 'OM'
  },
  {
    name: 'Pakistan',
    phonecode: '+92',
    isoCode: 'PK'
  },
  {
    name: 'Palau',
    phonecode: '+680',
    isoCode: 'PW'
  },
  {
    name: 'Palestinian Territory, Occupied',
    phonecode: '+970',
    isoCode: 'PS'
  },
  {
    name: 'Panama',
    phonecode: '+507',
    isoCode: 'PA'
  },
  {
    name: 'Papua New Guinea',
    phonecode: '+675',
    isoCode: 'PG'
  },
  {
    name: 'Paraguay',
    phonecode: '+595',
    isoCode: 'PY'
  },
  {
    name: 'Peru',
    phonecode: '+51',
    isoCode: 'PE'
  },
  {
    name: 'Philippines',
    phonecode: '+63',
    isoCode: 'PH'
  },
  {
    name: 'Pitcairn',
    phonecode: '+872',
    isoCode: 'PN'
  },
  {
    name: 'Poland',
    phonecode: '+48',
    isoCode: 'PL'
  },
  {
    name: 'Portugal',
    phonecode: '+351',
    isoCode: 'PT'
  },
  {
    name: 'Puerto Rico',
    phonecode: '+1939',
    isoCode: 'PR'
  },
  {
    name: 'Qatar',
    phonecode: '+974',
    isoCode: 'QA'
  },
  {
    name: 'Romania',
    phonecode: '+40',
    isoCode: 'RO'
  },
  {
    name: 'Russia',
    phonecode: '+7',
    isoCode: 'RU'
  },
  {
    name: 'Rwanda',
    phonecode: '+250',
    isoCode: 'RW'
  },
  {
    name: 'Reunion',
    phonecode: '+262',
    isoCode: 'RE'
  },
  {
    name: 'Saint Barthelemy',
    phonecode: '+590',
    isoCode: 'BL'
  },
  {
    name: 'Saint Helena, Ascension and Tristan Da Cunha',
    phonecode: '+290',
    isoCode: 'SH'
  },
  {
    name: 'Saint Kitts and Nevis',
    phonecode: '+1869',
    isoCode: 'KN'
  },
  {
    name: 'Saint Lucia',
    phonecode: '+1758',
    isoCode: 'LC'
  },
  {
    name: 'Saint Martin',
    phonecode: '+590',
    isoCode: 'MF'
  },
  {
    name: 'Saint Pierre and Miquelon',
    phonecode: '+508',
    isoCode: 'PM'
  },
  {
    name: 'Saint Vincent and the Grenadines',
    phonecode: '+1784',
    isoCode: 'VC'
  },
  {
    name: 'Samoa',
    phonecode: '+685',
    isoCode: 'WS'
  },
  {
    name: 'San Marino',
    phonecode: '+378',
    isoCode: 'SM'
  },
  {
    name: 'Sao Tome and Principe',
    phonecode: '+239',
    isoCode: 'ST'
  },
  {
    name: 'Saudi Arabia',
    phonecode: '+966',
    isoCode: 'SA'
  },
  {
    name: 'Senegal',
    phonecode: '+221',
    isoCode: 'SN'
  },
  {
    name: 'Serbia',
    phonecode: '+381',
    isoCode: 'RS'
  },
  {
    name: 'Seychelles',
    phonecode: '+248',
    isoCode: 'SC'
  },
  {
    name: 'Sierra Leone',
    phonecode: '+232',
    isoCode: 'SL'
  },
  {
    name: 'Singapore',
    phonecode: '+65',
    isoCode: 'SG'
  },
  {
    name: 'Slovakia',
    phonecode: '+421',
    isoCode: 'SK'
  },
  {
    name: 'Slovenia',
    phonecode: '+386',
    isoCode: 'SI'
  },
  {
    name: 'Solomon Islands',
    phonecode: '+677',
    isoCode: 'SB'
  },
  {
    name: 'Somalia',
    phonecode: '+252',
    isoCode: 'SO'
  },
  {
    name: 'South Africa',
    phonecode: '+27',
    isoCode: 'ZA'
  },
  {
    name: 'South Sudan',
    phonecode: '+211',
    isoCode: 'SS'
  },
  {
    name: 'South Georgia and the South Sandwich Islands',
    phonecode: '+500',
    isoCode: 'GS'
  },
  {
    name: 'Spain',
    phonecode: '+34',
    isoCode: 'ES'
  },
  {
    name: 'Sri Lanka',
    phonecode: '+94',
    isoCode: 'LK'
  },
  {
    name: 'Sudan',
    phonecode: '+249',
    isoCode: 'SD'
  },
  {
    name: 'Suriname',
    phonecode: '+597',
    isoCode: 'SR'
  },
  {
    name: 'Svalbard and Jan Mayen',
    phonecode: '+47',
    isoCode: 'SJ'
  },
  {
    name: 'Swaziland',
    phonecode: '+268',
    isoCode: 'SZ'
  },
  {
    name: 'Sweden',
    phonecode: '+46',
    isoCode: 'SE'
  },
  {
    name: 'Switzerland',
    phonecode: '+41',
    isoCode: 'CH'
  },
  {
    name: 'Syrian Arab Republic',
    phonecode: '+963',
    isoCode: 'SY'
  },
  {
    name: 'Taiwan',
    phonecode: '+886',
    isoCode: 'TW'
  },
  {
    name: 'Tajikistan',
    phonecode: '+992',
    isoCode: 'TJ'
  },
  {
    name: 'Tanzania, United Republic of Tanzania',
    phonecode: '+255',
    isoCode: 'TZ'
  },
  {
    name: 'Thailand',
    phonecode: '+66',
    isoCode: 'TH'
  },
  {
    name: 'Timor-Leste',
    phonecode: '+670',
    isoCode: 'TL'
  },
  {
    name: 'Togo',
    phonecode: '+228',
    isoCode: 'TG'
  },
  {
    name: 'Tokelau',
    phonecode: '+690',
    isoCode: 'TK'
  },
  {
    name: 'Tonga',
    phonecode: '+676',
    isoCode: 'TO'
  },
  {
    name: 'Trinidad and Tobago',
    phonecode: '+1868',
    isoCode: 'TT'
  },
  {
    name: 'Tunisia',
    phonecode: '+216',
    isoCode: 'TN'
  },
  {
    name: 'Turkey',
    phonecode: '+90',
    isoCode: 'TR'
  },
  {
    name: 'Turkmenistan',
    phonecode: '+993',
    isoCode: 'TM'
  },
  {
    name: 'Turks and Caicos Islands',
    phonecode: '+1649',
    isoCode: 'TC'
  },
  {
    name: 'Tuvalu',
    phonecode: '+688',
    isoCode: 'TV'
  },
  {
    name: 'Uganda',
    phonecode: '+256',
    isoCode: 'UG'
  },
  {
    name: 'Ukraine',
    phonecode: '+380',
    isoCode: 'UA'
  },
  {
    name: 'United Arab Emirates',
    phonecode: '+971',
    isoCode: 'AE'
  },
  {
    name: 'United Kingdom',
    phonecode: '+44',
    isoCode: 'GB'
  },
  {
    name: 'United States',
    phonecode: '+1',
    isoCode: 'US'
  },
  {
    name: 'Uruguay',
    phonecode: '+598',
    isoCode: 'UY'
  },
  {
    name: 'Uzbekistan',
    phonecode: '+998',
    isoCode: 'UZ'
  },
  {
    name: 'Vanuatu',
    phonecode: '+678',
    isoCode: 'VU'
  },
  {
    name: 'Venezuela, Bolivarian Republic of Venezuela',
    phonecode: '+58',
    isoCode: 'VE'
  },
  {
    name: 'Vietnam',
    phonecode: '+84',
    isoCode: 'VN'
  },
  {
    name: 'Virgin Islands, British',
    phonecode: '+1284',
    isoCode: 'VG'
  },
  {
    name: 'Virgin Islands, U.S.',
    phonecode: '+1340',
    isoCode: 'VI'
  },
  {
    name: 'Wallis and Futuna',
    phonecode: '+681',
    isoCode: 'WF'
  },
  {
    name: 'Yemen',
    phonecode: '+967',
    isoCode: 'YE'
  },
  {
    name: 'Zambia',
    phonecode: '+260',
    isoCode: 'ZM'
  },
  {
    name: 'Zimbabwe',
    phonecode: '+263',
    isoCode: 'ZW'
  }
]

// Utility functions to replace country-state-city functionality
export const getAllCountries = (): Country[] => countries
