import { FC } from 'react'
import { Box, Button, Card, Flex, Grid, Text } from 'theme-ui'
import { BsArrowRight } from 'react-icons/bs'

import { Pool } from 'network/services/pool'
import commaNumber from 'comma-number'
import MoreInfo from 'components/more-info'
import TokenIcon from 'components/icons/token'
import Link from 'components/link'

const AmplifyCard: FC<{ pool: Pool }> = ({ pool }) => (
  // TODO: we need minWidth for this
  <Card variant="secondary" sx={{ position: 'relative' }}>
    <Box sx={{ filter: 'brightness(0.5)' }}>
      <Flex variant="layout.flexCenterSpaceBetween">
        <Flex variant="layout.vStack" sx={{ '> * + *': { mt: 1 } }}>
          <Flex variant="layout.hStack">
            <TokenIcon type={pool.token_type} />
            <Box>
              <Text as="p" variant="large">
                {pool.token_name} Pool
              </Text>
            </Box>
            {/* <Box>
              <MoreInfo direction="top">
                <Box
                  variant="layout.borderedTextCenter"
                  bg="dark"
                  p={2}
                  sx={{ width: 'max-content' }}
                >
                  <Text variant="small" color="textMuted">
                    A limitation release for stakers
                  </Text>
                </Box>
              </MoreInfo>
            </Box> */}
          </Flex>
          <Box>
            <Text as="p" variant="small" color="textMuted">
              {`Stake ${pool.token_type}, Earn ${pool.token_type}`}
            </Text>
          </Box>
        </Flex>
        <Flex>
          <Link to={`/stake/overview/${pool.id}`}>
            <Button
              variant="round"
              sx={{
                width: 30,
                height: 30
              }}
            >
              <BsArrowRight />
            </Button>
          </Link>
        </Flex>
      </Flex>

      <Box p={2} />

      <Grid columns={2} gap={0.5}>
        <Text variant="mediumSmall" color="textMuted">
          Participants
        </Text>
        <Text variant="mediumSmall" sx={{ textAlign: 'right' }}>
          -{/* {pool.total_participants_count} */}
        </Text>
        <Text variant="mediumSmall" color="textMuted">
          Current Pool
        </Text>
        <Text variant="mediumSmall" sx={{ textAlign: 'right' }}>
          -{/* {`${commaNumber(pool.pool_size)} ${pool.token_type}`} */}
        </Text>
      </Grid>
    </Box>
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 999,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Text sx={{ transform: 'rotate(-6deg)' }}>Coming Soon</Text>
    </Box>
  </Card>
)

export default AmplifyCard
