import { Theme<PERSON>rovider, ThemeUIProvider, useColorMode } from 'theme-ui'
import { Navigate, Route, Routes, useLocation } from 'react-router-dom'
import { useSnapshot } from 'valtio'
import { lazy, Suspense } from 'react'
import theme from 'theme'
import { AnimatePresence } from 'framer-motion'

import AuthenticatedLayout from 'layout/authenticated'
import PublicLayout from 'layout/public'
import GeneralLayout from 'layout/general'
import { authStore, checkAuthorization } from 'store/auth'
import LoadingCard from 'components/loading'

// Lazy load all page components for better code splitting
const LoginPage = lazy(() => import('pages/SignIn'))
const SignUpPage = lazy(() => import('pages/SignUp'))
const SignUpDetail = lazy(() => import('pages/SignUp/sign-up-detail'))
const ThemeReference = lazy(() => import('pages/reference'))
const HomePage = lazy(() => import('pages/Home'))
const HelpCentrePage = lazy(() => import('pages/HelpCentre'))
const HelpCentreChat = lazy(() => import('pages/HelpCentre/help-chat'))
const ForgetPasswordPage = lazy(() => import('pages/ForgetPassword'))
const GiftRewardsPage = lazy(() => import('pages/GiftReward'))
const WalletPage = lazy(() => import('pages/Wallet'))
const WalletDeposit = lazy(() => import('pages/Wallet/deposit'))
const WalletWithdraw = lazy(() => import('pages/Wallet/withdraw'))
const StakePage = lazy(() => import('pages/Stake'))
const StakeMyContract = lazy(() => import('pages/Stake/my-contract'))
const StakeSubmissionPage = lazy(() => import('pages/Stake/stake-submission'))
const HowProfitDistributionWorksPage = lazy(
  () => import('pages/Stake/how-profit-distribution-works')
)
const NotificationPage = lazy(() => import('pages/Notification'))
const NFTsPage = lazy(() => import('pages/NFTs'))
const NFTWithdraw = lazy(() => import('pages/NFTs/nft-withdraw'))
const NFTDetail = lazy(() => import('pages/NFTs/nft-detail'))
const MyNFTsDetails = lazy(() => import('pages/NFTs/my-nfts-details'))
const MarketsPage = lazy(() => import('pages/Markets'))
const SecurityPage = lazy(() => import('pages/Security'))
const LoginInformationPage = lazy(() => import('pages/Security/login-information'))
const TwoFactorAuthenticationPage = lazy(() => import('pages/Security/two-factor-authentication'))
const LastLoginPage = lazy(() => import('pages/Security/last-login'))
const StakePoolsPage = lazy(() => import('pages/Stake/stake-pools'))
const IdentityVerificationPage = lazy(() => import('pages/IdentityVerification'))
const IDVerification = lazy(() => import('pages/IdentityVerification/id-verification'))
const PassportVerification = lazy(() => import('pages/IdentityVerification/passport-verification'))
const StakeOverview = lazy(() => import('pages/Stake/stake-overview'))
const AnnouncementsPage = lazy(() => import('pages/Announcements'))
const AnnouncementDetail = lazy(() => import('pages/Announcements/announcement-detail'))
const AboutVoxtoPage = lazy(() => import('pages/AboutVoxto'))
const HowItWorksPage = lazy(() => import('pages/HowItWorks'))
const TermsConditionPage = lazy(() => import('pages/TermsCondition'))
const PrivacyPolicyPage = lazy(() => import('pages/PrivacyPolicy'))
const SystemAcquisitionPage = lazy(() => import('pages/SecurityPolicy/SystemAcquisition'))
const OperationSecurityPage = lazy(() => import('pages/SecurityPolicy/OperationsSecurity'))
const InformationSecurityPage = lazy(() => import('pages/SecurityPolicy/InformationSecurity'))
const NetworkSecurityPage = lazy(() => import('pages/SecurityPolicy/NetworkSecurity'))
const PasswordPolicyPage = lazy(() => import('pages/SecurityPolicy/PasswordPolicy'))
const UserAgreementPage = lazy(() => import('pages/UserAgreement'))
const NewsPage = lazy(() => import('pages/News'))

// Loading component for lazy-loaded routes
const PageLoader = () => <></>

const RequireAuth = ({ children }: { children: JSX.Element }) => {
  const { token: isLoggedIn } = useSnapshot(authStore)
  let location = useLocation()

  if (!isLoggedIn) {
    return <Navigate to="/" state={{ from: location }} replace />
  }

  return children
}

function App() {
  return (
    <ThemeUIProvider theme={theme}>
      <Init />
    </ThemeUIProvider>
  )
}

function Init() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [colorMode, setColorMode] = useColorMode()

  setColorMode('default')

  return (
    <AnimatePresence exitBeforeEnter>
      <Suspense fallback={<PageLoader />}>
        <Routes>
          <Route
            element={
              <RequireAuth>
                <AuthenticatedLayout />
              </RequireAuth>
            }
          >
            <Route path="/about-voxto" element={<AboutVoxtoPage />} />
            <Route path="/how-it-works" element={<HowItWorksPage />} />
            <Route path="/home" element={<HomePage />} />
            <Route path="/help" element={<HelpCentrePage />} />
            <Route path="/help/:id" element={<HelpCentreChat />} />
            <Route path="/markets" element={<MarketsPage />} />
            <Route path="/gift-rewards" element={<GiftRewardsPage />} />
            <Route path="/notification" element={<NotificationPage />} />
            <Route path="/announcements">
              <Route path="" element={<AnnouncementsPage />} />
              <Route path=":id" element={<AnnouncementDetail />} />
            </Route>
            <Route path="/security">
              <Route path="" element={<SecurityPage />} />
              <Route path="login-information" element={<LoginInformationPage />} />
              <Route path="2-factor-authentication" element={<TwoFactorAuthenticationPage />} />
              <Route path="last-login" element={<LastLoginPage />} />
            </Route>
            <Route path="/wallet">
              <Route path="" element={<WalletPage />} />
              <Route path="deposit" element={<WalletDeposit />} />
              <Route path="withdraw" element={<WalletWithdraw />} />
            </Route>
            <Route path="/stake">
              <Route path="" element={<StakePage />} />
              <Route path=":id" element={<StakeMyContract />} />
              <Route path="pools" element={<StakePoolsPage />} />
              <Route path="pools/:id/how" element={<HowProfitDistributionWorksPage />} />
              <Route path="submit/:id" element={<StakeSubmissionPage />} />
              <Route path="overview/:id" element={<StakeOverview />} />
            </Route>
            <Route path="/identity-verification">
              <Route path="" element={<IdentityVerificationPage />} />
              <Route path="ic" element={<IDVerification />} />
              <Route path="passport" element={<PassportVerification />} />
            </Route>
            <Route path="/nfts">
              <Route path="" element={<NFTsPage />} />
              <Route path="withdraw" element={<NFTWithdraw />} />
              <Route path="shop/:id" element={<NFTDetail />} />
              <Route path=":id" element={<MyNFTsDetails />} />
            </Route>
            <Route path="/news">
              <Route path="" element={<NewsPage />} />
            </Route>
          </Route>

          <Route path="/reference" element={<ThemeReference />} />

          <Route element={<GeneralLayout />}>
            <Route path="/terms-condition" element={<TermsConditionPage />} />
            <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="/user-agreement" element={<UserAgreementPage />} />
            <Route
              path="/system-acquisition-development-maintenance"
              element={<SystemAcquisitionPage />}
            />
            <Route path="/operations-security" element={<OperationSecurityPage />} />
            <Route path="/information-security-policy" element={<InformationSecurityPage />} />
            <Route path="/network-communication-security" element={<NetworkSecurityPage />} />
            <Route path="/password-policy" element={<PasswordPolicyPage />} />
          </Route>

          <Route element={<PublicLayout />}>
            <Route path="/forget-password" element={<ForgetPasswordPage />} />
            <Route path="/sign-up" element={<SignUpDetail />} />
            <Route path="/sign-in" element={<LoginPage />} />
            <Route path="/" element={<SignUpPage />} />
          </Route>
        </Routes>
      </Suspense>
    </AnimatePresence>
  )
}

new Promise(() => checkAuthorization())

export default App
